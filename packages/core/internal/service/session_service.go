package service

import (
	"fmt"
	"time"

	"claude-pilot/core/internal/logger"
	"claude-pilot/shared/interfaces"

	"log/slog"

	"github.com/google/uuid"
)

// SessionService implements the SessionService interface
type SessionService struct {
	repository  interfaces.SessionRepository
	multiplexer interfaces.TerminalMultiplexer
	logger      *logger.Logger
}

// NewSessionService creates a new session service
func NewSessionService(repository interfaces.SessionRepository, multiplexer interfaces.TerminalMultiplexer) *SessionService {
	// Create a disabled logger by default for backward compatibility
	disabledLogger, _ := logger.Setup.Disabled().Build()
	return &SessionService{
		repository:  repository,
		multiplexer: multiplexer,
		logger:      disabledLogger,
	}
}

// NewSessionServiceWithLogger creates a new session service with a logger
func NewSessionServiceWithLogger(repository interfaces.SessionRepository, multiplexer interfaces.TerminalMultiplexer, log *logger.Logger) *SessionService {
	return &SessionService{
		repository:  repository,
		multiplexer: multiplexer,
		logger:      log,
	}
}

// CreateSession creates a new session with both metadata and multiplexer session
func (s *SessionService) CreateSession(name, description, projectPath string) (*interfaces.Session, error) {
	start := time.Now()

	if name == "" {
		name = fmt.Sprintf("session-%s", time.Now().Format("20060102-150405"))
	}

	s.logger.Debug("Creating session",
		"name", name,
		"description", description,
		"project_path", projectPath)

	// Check if session with same name already exists
	if s.repository.Exists(name) {
		s.logger.Warn("Session creation failed: name already exists",
			"name", name)
		return nil, fmt.Errorf("session with name '%s' already exists", name)
	}

	// Create session metadata
	session := &interfaces.Session{
		ID:          uuid.New().String(),
		Name:        name,
		Status:      interfaces.StatusActive,
		CreatedAt:   time.Now(),
		LastActive:  time.Now(),
		ProjectPath: projectPath,
		Description: description,
		Messages:    []interfaces.Message{},
	}

	// Save session metadata first
	if err := s.repository.Save(session); err != nil {
		s.logger.Error("Failed to save session metadata",
			"session_id", session.ID,
			"name", name,
			"error", err)
		return nil, fmt.Errorf("failed to save session metadata: %w", err)
	}

	// Create the multiplexer session
	req := interfaces.CreateSessionRequest{
		Name:        name,
		Description: description,
		WorkingDir:  projectPath,
		Command:     "claude",
	}

	s.logger.Debug("Creating multiplexer session",
		"session_id", session.ID,
		"name", name,
		"command", req.Command,
		"working_dir", req.WorkingDir)

	_, err := s.multiplexer.CreateSession(req)
	if err != nil {
		// If multiplexer session creation fails, mark session as inactive but keep metadata
		session.Status = interfaces.StatusInactive
		s.repository.Save(session)
		s.logger.Error("Failed to create multiplexer session",
			"session_id", session.ID,
			"name", name,
			"error", err)
		return session, fmt.Errorf("session created but failed to create multiplexer session: %w", err)
	}

	// Update session status
	session.Status = interfaces.StatusActive
	if err := s.repository.Save(session); err != nil {
		s.logger.Error("Failed to update session status",
			"session_id", session.ID,
			"name", name,
			"error", err)
		return session, fmt.Errorf("session created but failed to update status: %w", err)
	}

	// Save index after session creation (important operations)
	if err := s.repository.SaveIndex(); err != nil {
		// Index save failure is not critical, just log it
		s.logger.Warn("Failed to save name index after session creation",
			"session_id", session.ID,
			"name", name,
			"error", err)
	}

	s.logger.Performance("CreateSession", start,
		slog.String("session_id", session.ID),
		slog.String("name", name),
		slog.String("project_path", projectPath))

	s.logger.Info("Session created successfully",
		"session_id", session.ID,
		"name", name,
		"status", string(session.Status))

	return session, nil
}

// GetSession retrieves a session by ID or name
func (s *SessionService) GetSession(identifier string) (*interfaces.Session, error) {
	// Try by ID first
	if session, err := s.repository.FindByID(identifier); err == nil {
		return s.updateSessionStatus(session), nil
	}

	// Try by name
	if session, err := s.repository.FindByName(identifier); err == nil {
		return s.updateSessionStatus(session), nil
	}

	return nil, fmt.Errorf("session '%s' not found", identifier)
}

// ListSessions returns all sessions with their current status
func (s *SessionService) ListSessions() ([]*interfaces.Session, error) {
	start := time.Now()

	s.logger.Debug("Listing sessions")

	sessions, err := s.repository.List()
	if err != nil {
		s.logger.Error("Failed to list sessions from repository", "error", err)
		return nil, fmt.Errorf("failed to list sessions: %w", err)
	}

	s.logger.Debug("Retrieved sessions from repository", "count", len(sessions))

	// Batch update status for all sessions
	s.batchUpdateSessionStatus(sessions)

	s.logger.Performance("ListSessions", start, slog.Int("session_count", len(sessions)))

	s.logger.Debug("Sessions listed successfully", "count", len(sessions))

	return sessions, nil
}

// UpdateSession updates session metadata
func (s *SessionService) UpdateSession(session *interfaces.Session) error {
	if !s.repository.Exists(session.ID) {
		return fmt.Errorf("session '%s' not found", session.ID)
	}

	session.LastActive = time.Now()
	return s.repository.Save(session)
}

// DeleteSession removes a session and its multiplexer session
func (s *SessionService) DeleteSession(identifier string) error {
	start := time.Now()

	s.logger.Debug("Deleting session", "identifier", identifier)

	session, err := s.GetSession(identifier)
	if err != nil {
		s.logger.Error("Failed to find session for deletion",
			"identifier", identifier,
			"error", err)
		return err
	}

	sessionLogger := s.logger.WithSession(session.ID, session.Name)

	// Kill the multiplexer session if it's running
	if s.multiplexer.IsSessionRunning(session.Name) {
		sessionLogger.Debug("Killing running multiplexer session")
		if err := s.multiplexer.KillSession(session.Name); err != nil {
			sessionLogger.Error("Failed to kill multiplexer session", "error", err)
			return fmt.Errorf("failed to kill multiplexer session: %w", err)
		}
	}

	// Remove session metadata
	if err := s.repository.Delete(session.ID); err != nil {
		sessionLogger.Error("Failed to delete session metadata", "error", err)
		return fmt.Errorf("failed to delete session metadata: %w", err)
	}

	// Save index after deletion (important operations)
	if err := s.repository.SaveIndex(); err != nil {
		// Index save failure is not critical, just log it
		sessionLogger.Warn("Failed to save name index after session deletion", "error", err)
	}

	s.logger.Performance("DeleteSession", start,
		slog.String("session_id", session.ID),
		slog.String("name", session.Name))

	sessionLogger.Info("Session deleted successfully")

	return nil
}

// AttachToSession connects to an existing session
func (s *SessionService) AttachToSession(identifier string) error {
	start := time.Now()

	s.logger.Debug("Attaching to session", "identifier", identifier)

	session, err := s.GetSession(identifier)
	if err != nil {
		s.logger.Error("Failed to find session for attachment",
			"identifier", identifier,
			"error", err)
		return err
	}

	sessionLogger := s.logger.WithSession(session.ID, session.Name)

	// Update session status to connected
	session.Status = interfaces.StatusConnected
	session.LastActive = time.Now()
	if err := s.repository.Save(session); err != nil {
		// Don't fail attachment due to metadata update failure
		sessionLogger.Warn("Failed to update session metadata before attachment", "error", err)
	}

	sessionLogger.Info("Attaching to multiplexer session")

	// Attach to the multiplexer session
	err = s.multiplexer.AttachToSession(session.Name)
	if err != nil {
		sessionLogger.Error("Failed to attach to multiplexer session", "error", err)
		return err
	}

	s.logger.Performance("AttachToSession", start,
		slog.String("session_id", session.ID),
		slog.String("name", session.Name))

	return nil
}

// AddMessage adds a message to a session's conversation history
func (s *SessionService) AddMessage(sessionID, role, content string) error {
	session, err := s.GetSession(sessionID)
	if err != nil {
		return err
	}

	message := interfaces.Message{
		ID:        uuid.New().String(),
		Role:      role,
		Content:   content,
		Timestamp: time.Now(),
	}

	session.Messages = append(session.Messages, message)
	session.LastActive = time.Now()

	return s.repository.Save(session)
}

// IsSessionRunning checks if the session's multiplexer is active
func (s *SessionService) IsSessionRunning(identifier string) bool {
	session, err := s.GetSession(identifier)
	if err != nil {
		return false
	}
	return s.multiplexer.IsSessionRunning(session.Name)
}

// updateSessionStatus updates a session's status based on multiplexer state
func (s *SessionService) updateSessionStatus(session *interfaces.Session) *interfaces.Session {
	if s.multiplexer.IsSessionRunning(session.Name) {
		// Check if someone is attached (this is backend-specific and may not be available)
		if muxSession, err := s.multiplexer.GetSession(session.Name); err == nil {
			if muxSession.IsAttached() {
				session.Status = interfaces.StatusConnected
			} else {
				session.Status = interfaces.StatusActive
			}
		} else {
			session.Status = interfaces.StatusActive
		}
	} else {
		session.Status = interfaces.StatusInactive
	}

	return session
}

// batchUpdateSessionStatus efficiently updates status for multiple sessions
func (s *SessionService) batchUpdateSessionStatus(sessions []*interfaces.Session) {
	// Get all multiplexer sessions once
	muxSessions, err := s.multiplexer.ListSessions()
	if err != nil {
		// If we can't get multiplexer sessions, fall back to individual checks
		for i, session := range sessions {
			sessions[i] = s.updateSessionStatus(session)
		}
		return
	}

	// Create a map of session names to multiplexer sessions for O(1) lookup
	// Pre-allocate map with expected capacity
	muxSessionMap := make(map[string]interfaces.MultiplexerSession, len(muxSessions))
	for _, muxSession := range muxSessions {
		muxSessionMap[muxSession.GetName()] = muxSession
	}

	// Update all sessions using the batch data
	for _, session := range sessions {
		if muxSession, exists := muxSessionMap[session.Name]; exists {
			// Session exists in multiplexer
			if muxSession.IsAttached() {
				session.Status = interfaces.StatusConnected
			} else {
				session.Status = interfaces.StatusActive
			}
		} else {
			// Session not found in multiplexer
			session.Status = interfaces.StatusInactive
		}
	}
}

// KillAllSessions terminates all sessions
func (s *SessionService) KillAllSessions() error {
	sessions, err := s.ListSessions()
	if err != nil {
		return fmt.Errorf("failed to list sessions: %w", err)
	}

	var errors []string
	for _, session := range sessions {
		if err := s.DeleteSession(session.ID); err != nil {
			errors = append(errors, fmt.Sprintf("failed to delete session %s: %v", session.Name, err))
		}
	}

	if len(errors) > 0 {
		return fmt.Errorf("errors deleting sessions: %v", errors)
	}

	return nil
}

// GetMultiplexer returns the underlying multiplexer (for legacy compatibility)
func (s *SessionService) GetMultiplexer() interfaces.TerminalMultiplexer {
	return s.multiplexer
}
